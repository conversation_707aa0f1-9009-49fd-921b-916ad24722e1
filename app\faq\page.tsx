"use client";

import React from 'react';

interface FAQItemProps {
  question: string;
  answer: string;
}

const FAQItem: React.FC<FAQItemProps> = ({ question, answer }) => {
  const [isOpen, setIsOpen] = React.useState(false);

  return (
    <div className="border-b border-gray-200 dark:border-gray-700 py-4">
      <button
        className="flex justify-between items-center w-full text-left focus:outline-none"
        onClick={() => setIsOpen(!isOpen)}
      >
        <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">{question}</h3>
        <span className="text-gray-500 dark:text-gray-400 text-xl">
          {isOpen ? '-' : '+'}
        </span>
      </button>
      {isOpen && (
        <div className="mt-2 text-gray-600 dark:text-gray-300">
          <p>{answer}</p>
        </div>
      )}
    </div>
  );
};

const mockFAQs = [
  {
    question: "What is kanoon.ai?",
    answer: "kanoon.ai is an innovative platform leveraging advanced AI to provide specialized legal insights.",
  },
  {
    question: "How does kanoon.ai differ from general AI chatbots?",
    answer: "Unlike general AI, kanoon.ai is meticulously trained on vast legal datasets, enabling it to understand and respond to complex legal queries with precision and nuance.",
  },
  {
    question: "Is the legal information provided by kanoon.ai accurate?",
    answer: "The information provided is for informational purposes only and does not constitute legal advice. Always consult with a qualified legal professional for specific legal concerns.",
  },
  {
    question: "Can I use kanoon.ai for personal legal advice?",
    answer: "kanoon.ai is designed to provide general legal insights and information. For personalized legal advice, it is crucial to consult with a licensed attorney.",
  },
];

const FAQPage: React.FC = () => {
  return (
    <div className="container mx-auto p-8">
      <h1 className="text-4xl font-bold mb-8 text-gray-800 dark:text-white">Frequently Asked Questions</h1>
      <div className="space-y-4">
        {mockFAQs.map((faq, index) => (
          <FAQItem key={index} question={faq.question} answer={faq.answer} />
        ))}
      </div>
    </div>
  );
};

export default FAQPage;
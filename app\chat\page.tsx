"use client";

import React, { useState } from 'react';
import ChatInput from '../../components/chat/ChatInput';
import LoadingIndicator from '../../components/chat/LoadingIndicator';
import TopicSuggestions from '../../components/chat/TopicSuggestions';
import MessageDisplay from '../../components/chat/MessageDisplay';
import AIJudgeWorkspace from '../../components/ai-judge/AIJudgeWorkspace';

interface Message {
  id: number;
  text: string;
  sender: 'user' | 'llm';
}

const ChatPage = () => {
  const [messages, setMessages] = useState<Message[]>([
    { id: 1, text: 'Hello! How can I help you today?', sender: 'llm' },
  ]);
  const [isLoading, setIsLoading] = useState(false);
  const [isAIJudgeMode, setIsAIJudgeMode] = useState(false);

  const handleSendMessage = (text: string) => {
    const newUserMessage: Message = {
      id: messages.length + 1,
      text,
      sender: 'user',
    };
    setMessages((prevMessages) => [...prevMessages, newUserMessage]);
    setIsLoading(true);

    // Simulate LLM response
    setTimeout(() => {
      const newLlmMessage: Message = {
        id: messages.length + 2,
        text: `This is a mock response to: "${text}"`, // Replace with actual LLM integration later
        sender: 'llm',
      };
      setMessages((prevMessages) => [...prevMessages, newLlmMessage]);
      setIsLoading(false);
    }, 1000);
  };

  const handleSelectTopic = (topic: string) => {
    handleSendMessage(`Tell me about ${topic}`);
  };

  return (
    <div className="flex flex-col h-screen">
      <div className="flex justify-between items-center p-4">
        <TopicSuggestions onSelectTopic={handleSelectTopic} />
        <button
          onClick={() => setIsAIJudgeMode(!isAIJudgeMode)}
          className="px-4 py-2 rounded-md bg-purple-600 text-white hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50"
        >
          {isAIJudgeMode ? 'Exit AI Judge Mode' : 'Enter AI Judge Mode'}
        </button>
      </div>
      {isAIJudgeMode ? (
        <AIJudgeWorkspace />
      ) : (
        <>
          <MessageDisplay messages={messages} />
          {isLoading && <LoadingIndicator />}
        </>
      )}
      {!isAIJudgeMode && <ChatInput onSendMessage={handleSendMessage} />}
    </div>
  );
};

export default ChatPage;
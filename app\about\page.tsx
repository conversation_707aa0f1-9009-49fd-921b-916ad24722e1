"use client";

import React from 'react';
import Link from 'next/link';

const AboutPage: React.FC = () => {
  return (
    <div className="container mx-auto p-8">
      <h1 className="text-4xl font-bold mb-8 text-gray-800 dark:text-white">About kanoon.ai</h1>

      <section className="mb-12">
        <h2 className="text-3xl font-semibold mb-4 text-gray-700 dark:text-gray-200">Our Mission/Vision</h2>
        <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
          Our mission is to democratize legal information, making it accessible and understandable for everyone, everywhere. We envision a world where legal knowledge is no longer a barrier but a tool for empowerment.
        </p>
      </section>

      <section className="mb-12">
        <h2 className="text-3xl font-semibold mb-4 text-gray-700 dark:text-gray-200">What is kanoon.ai?</h2>
        <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
          kanoon.ai is an innovative platform leveraging advanced AI to provide specialized legal insights. Unlike general AI, our system is meticulously trained on vast legal datasets, enabling it to understand and respond to complex legal queries with precision and nuance.
        </p>
      </section>

      <section className="mb-12">
        <h2 className="text-3xl font-semibold mb-4 text-gray-700 dark:text-gray-200">How It Works (Conceptual)</h2>
        <div className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
          <p className="mb-4">Our law-specialized LLM processes your legal questions by:</p>
          <ul className="list-disc list-inside ml-4">
            <li>Analyzing your query for legal keywords and context.</li>
            <li>Cross-referencing with an extensive database of legal statutes, precedents, and scholarly articles.</li>
            <li>Generating a concise and relevant response, often citing conceptual legal sources.</li>
          </ul>
          {/* Placeholder for a simple diagram/icon */}
          <div className="mt-6 p-4 bg-gray-200 dark:bg-gray-700 rounded-md text-center text-gray-500 dark:text-gray-400">
            [Conceptual Diagram/Icon: User Query -&gt; AI Processing -&gt; Legal Answer]
          </div>
        </div>
      </section>

      <section className="mb-12">
        <h2 className="text-3xl font-semibold mb-4 text-gray-700 dark:text-gray-200">Benefits</h2>
        <ul className="list-disc list-inside text-lg text-gray-600 dark:text-gray-300 leading-relaxed space-y-2">
          <li><strong>Instant Access:</strong> Get legal insights quickly, without lengthy research.</li>
          <li><strong>Specialized Knowledge:</strong> Benefit from an AI trained specifically on legal data.</li>
          <li><strong>User-Friendly:</strong> An intuitive interface makes legal information accessible to everyone.</li>
          <li><strong>Empowerment:</strong> Understand your legal rights and options with clarity.</li>
        </ul>
      </section>

      <div className="text-center mt-12">
        <Link href="/chat" className="inline-block bg-blue-600 text-white text-xl font-semibold py-3 px-8 rounded-lg hover:bg-blue-700 transition duration-300">
          Start Chatting Now
        </Link>
      </div>
    </div>
  );
};

export default AboutPage;
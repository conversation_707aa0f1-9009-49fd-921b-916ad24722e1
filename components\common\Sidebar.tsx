import React from 'react';
import Link from 'next/link';

interface ChatSession {
  id: string;
  title: string;
}

const mockChatSessions: ChatSession[] = [
  { id: '1', title: 'Legal Question about Property' },
  { id: '2', title: 'Inquiry on Contract Law' },
  { id: '3', title: 'Family Law Advice' },
  { id: '4', title: 'Intellectual Property Rights' },
  { id: '5', title: 'Criminal Defense Basics' },
];

const Sidebar: React.FC = () => {
  const activeSessionId = '2'; // Mock active session for demonstration

  return (
    <aside className="w-64 bg-gray-100 dark:bg-gray-800 p-4 flex flex-col h-full">
      <div className="mb-4">
        <button className="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
          + New Chat
        </button>
      </div>
      <nav className="flex-1 overflow-y-auto">
        <ul className="space-y-2">
          {mockChatSessions.map((session) => (
            <li key={session.id}>
              <a
                href={`/chat?session=${session.id}`}
                className={`block p-2 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 ${session.id === activeSessionId ? 'bg-blue-200 dark:bg-blue-700 font-semibold' : ''}`}
              >
                {session.title}
              </a>
            </li>
          ))}
        </ul>
      </nav>
      <div className="mt-4 border-t border-gray-300 dark:border-gray-700 pt-4">
        <ul className="space-y-2">
          <li>
            <Link href="/about" className="block p-2 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700">
              About Us
            </Link>
          </li>
          <li>
            <Link href="/how-it-works" className="block p-2 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700">
              How It Works
            </Link>
          </li>
          <li>
            <Link href="/faq" className="block p-2 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700">
              FAQ
            </Link>
          </li>
        </ul>
      </div>
    </aside>
  );
};

export default Sidebar;
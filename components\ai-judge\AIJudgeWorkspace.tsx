import React from 'react';

const AIJudgeWorkspace: React.FC = () => {
  return (
    <div className="flex-1 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg flex flex-col">
      <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">AI Judge Mode</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 flex-1">
        <div className="flex flex-col bg-white dark:bg-gray-700 p-4 rounded-lg">
          <h3 className="text-lg font-medium mb-2 text-gray-800 dark:text-gray-200">Case Details Input (Mock)</h3>
          <textarea
            className="w-full flex-1 p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-600 text-gray-900 dark:text-gray-100"
            placeholder="Enter case facts, parties, etc."
            rows={8}
          ></textarea>
        </div>
        <div className="flex flex-col bg-white dark:bg-gray-700 p-4 rounded-lg">
          <h3 className="text-lg font-medium mb-2 text-gray-800 dark:text-gray-200">Legal Precedents/Statutes (Mock Display)</h3>
          <div className="flex-1 p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-600 text-gray-900 dark:text-gray-100 overflow-y-auto">
            <p>Mock legal text 1: Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>
            <p>Mock legal text 2: Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
            <p>Mock legal text 3: Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
          </div>
        </div>
      </div>
      <div className="mt-4 bg-white dark:bg-gray-700 p-4 rounded-lg">
        <h3 className="text-lg font-medium mb-2 text-gray-800 dark:text-gray-200">Evidence Upload (Mock)</h3>
        <input
          type="file"
          className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-600 text-gray-900 dark:text-gray-100"
        />
      </div>
    </div>
  );
};

export default AIJudgeWorkspace;
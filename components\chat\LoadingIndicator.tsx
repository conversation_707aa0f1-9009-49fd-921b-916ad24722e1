import React from 'react';

const LoadingIndicator = () => {
  return (
    <div className="flex items-center justify-center py-2">
      <div className="dot-pulse">
        <div className="dot-pulse-dot"></div>
        <div className="dot-pulse-dot"></div>
        <div className="dot-pulse-dot"></div>
      </div>
      <style jsx>{`
        .dot-pulse {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .dot-pulse-dot {
          width: 8px;
          height: 8px;
          background-color: #3b82f6; /* blue-500 */
          border-radius: 50%;
          margin: 0 4px;
          animation: dotPulse 1.5s infinite ease-in-out;
        }
        .dot-pulse-dot:nth-child(1) {
          animation-delay: 0s;
        }
        .dot-pulse-dot:nth-child(2) {
          animation-delay: 0.2s;
        }
        .dot-pulse-dot:nth-child(3) {
          animation-delay: 0.4s;
        }

        @keyframes dotPulse {
          0%, 100% {
            transform: scale(0.8);
            opacity: 0.5;
          }
          50% {
            transform: scale(1.2);
            opacity: 1;
          }
        }
      `}</style>
    </div>
  );
};

export default LoadingIndicator;

"use client";

import React from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';

const HeroSection: React.FC = () => {
  return (
    <section className="relative h-screen flex items-center justify-center text-center bg-gray-50 dark:bg-gray-900 overflow-hidden">
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className="z-10 p-4 max-w-4xl mx-auto"
      >
        <h1 className="text-5xl md:text-7xl font-extrabold text-gray-900 dark:text-white leading-tight mb-4">
          Your AI Legal Assistant for Every Question
        </h1>
        <p className="text-xl md:text-2xl text-gray-700 dark:text-gray-300 mb-8">
          Law-Specialized LLM Chat for Citizens and Professionals
        </p>
        <p className="text-lg text-gray-600 dark:text-gray-400 mb-10">
          kanoon.ai offers instant, simplified answers to legal questions for citizens, and powerful &apos;AI Judge&apos; mode for legal professionals to rapidly assess cases and suggest pertinent legal statutes.
        </p>
        <Link href="/chat">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="px-8 py-4 bg-blue-600 text-white text-xl font-semibold rounded-lg shadow-lg hover:bg-blue-700 transition duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
          >
            Start Chatting Now
          </motion.button>
        </Link>
      </motion.div>

      {/* Conceptual background graphic/animation placeholder */}
      <div className="absolute inset-0 z-0 opacity-10">
        {/* Replace with actual SVG or GSAP animation for subtle legal/AI theme */}
        <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid slice">
          <circle cx="50" cy="50" r="40" fill="currentColor" className="text-blue-200 dark:text-blue-800 opacity-30" />
          <rect x="20" y="20" width="60" height="60" fill="currentColor" className="text-purple-200 dark:text-purple-800 opacity-30 rotate-45" />
        </svg>
      </div>
    </section>
  );
};

export default HeroSection;
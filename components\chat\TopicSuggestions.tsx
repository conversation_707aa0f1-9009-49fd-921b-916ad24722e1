import React from 'react';

interface TopicSuggestionsProps {
  onSelectTopic: (topic: string) => void;
}

const topics = [
  "Contract Law",
  "Intellectual Property",
  "Family Law",
  "Criminal Law",
  "Real Estate Law",
];

const TopicSuggestions: React.FC<TopicSuggestionsProps> = ({ onSelectTopic }) => {
  return (
    <div className="flex flex-wrap gap-2 p-4 border-b border-gray-200 dark:border-gray-700">
      {topics.map((topic) => (
        <button
          key={topic}
          className="px-3 py-1 rounded-full bg-gray-200 text-gray-800 text-sm hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
          onClick={() => onSelectTopic(topic)}
        >
          {topic}
        </button>
      ))}
    </div>
  );
};

export default TopicSuggestions;
